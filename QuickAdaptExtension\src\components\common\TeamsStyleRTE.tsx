import React, { useState, useEffect, useRef, useMemo } from "react";
import { Box, IconButton, Tooltip } from "@mui/material";
import JoditEditor from "jodit-react";
import EditIcon from "@mui/icons-material/Edit";
import CheckIcon from "@mui/icons-material/Check";
import { useTranslation } from 'react-i18next';

interface TeamsStyleRTEProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    disabled?: boolean;
    maxHeight?: string;
    minHeight?: string;
    joditConfig?: any;
    className?: string;
    style?: React.CSSProperties;
    onFocus?: () => void;
    onBlur?: () => void;
}

const TeamsStyleRTE: React.FC<TeamsStyleRTEProps> = ({
    value,
    onChange,
    placeholder = "Start writing...",
    disabled = false,
    maxHeight = "120px", // Approximately 4 lines
    minHeight = "40px",
    joditConfig = {},
    className = "",
    style = {},
    onFocus,
    onBlur,
}) => {
    const { t: translate } = useTranslation();
    const [isEditMode, setIsEditMode] = useState(false);
    const [tempValue, setTempValue] = useState(value);
    const [actionIconPosition, setActionIconPosition] = useState({ top: 8, right: 8 });
    const viewContainerRef = useRef<HTMLDivElement>(null);
    const editorRef = useRef<any>(null);

    // Default Jodit configuration
    const defaultJoditConfig = useMemo(() => ({
        readonly: false,
        placeholder: placeholder,
        height: 'auto',
        minHeight: parseInt(minHeight),
        maxHeight: parseInt(maxHeight),
        showCharsCounter: false,
        showWordsCounter: false,
        showXPathInStatusbar: false,
        askBeforePasteHTML: false,
        askBeforePasteFromWord: false,
        defaultActionOnPaste: "insert_clear_html",
        toolbarSticky: false,
        toolbarAdaptive: false,
        buttons: [
            'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',
            'font', 'fontsize', 'link',
            {
                name: 'more',
                iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',
                list: [
                    'source', 'image', 'video', 'table',
                    'align', 'undo', 'redo', '|',
                    'hr', 'eraser', 'copyformat',
                    'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',
                    'outdent', 'indent', 'paragraph',
                ]
            }
        ],
        autofocus: true,
        cursorAfterAutofocus: 'end' as const,
        enter: 'br', // Use <br> for line breaks instead of <p>
        events: {
            keydown: (event: KeyboardEvent) => {
                // Handle Enter key behavior
                if (event.key === 'Enter') {
                    if (event.shiftKey) {
                        // Shift+Enter: Insert line break
                        return true;
                    } else {
                        // Regular Enter: Insert line break (Teams-style behavior)
                        return true;
                    }
                }
                // Handle Escape key to exit edit mode
                if (event.key === 'Escape') {
                    handleSaveAndExit();
                    return false;
                }
            },
            blur: () => {
                // Auto-save when editor loses focus
                setTimeout(() => {
                    handleSaveAndExit();
                }, 100);
            },
            focus: () => {
                // Ensure editor is properly focused
                if (editorRef.current) {
                    const editor = editorRef.current;
                    if (editor.selection) {
                        editor.selection.focus();
                    }
                }
            }
        },
        ...joditConfig
    }), [placeholder, minHeight, maxHeight, joditConfig]);

    // Update temp value when prop value changes
    useEffect(() => {
        setTempValue(value);
    }, [value]);

    // Calculate action icon position based on content
    const calculateActionIconPosition = () => {
        if (!viewContainerRef.current || isEditMode) return;

        const container = viewContainerRef.current;
        const containerRect = container.getBoundingClientRect();
        const containerHeight = container.clientHeight;
        const scrollTop = container.scrollTop;

        // Position the icon at the top-right of the visible area
        const visibleTop = scrollTop + 8;
        const maxTop = Math.min(visibleTop, containerHeight - 32); // 32px for icon height

        setActionIconPosition({
            top: maxTop,
            right: 8
        });
    };

    // Update icon position when content or scroll changes
    useEffect(() => {
        calculateActionIconPosition();
    }, [value, isEditMode]);

    // Handle scroll events to update icon position
    useEffect(() => {
        const container = viewContainerRef.current;
        if (!container) return;

        const handleScroll = () => {
            calculateActionIconPosition();
        };

        container.addEventListener('scroll', handleScroll);
        return () => container.removeEventListener('scroll', handleScroll);
    }, [isEditMode]);

    const handleEditClick = () => {
        if (disabled) return;
        
        if (isEditMode) {
            handleSaveAndExit();
        } else {
            setIsEditMode(true);
            setTempValue(value);
            onFocus?.();
        }
    };

    const handleSaveAndExit = () => {
        setIsEditMode(false);
        onChange(tempValue);
        onBlur?.();
    };

    const handleEditorChange = (newContent: string) => {
        setTempValue(newContent);
    };

    const handleViewClick = () => {
        if (!isEditMode && !disabled) {
            setIsEditMode(true);
            setTempValue(value);
            onFocus?.();
        }
    };

    // Calculate if content should scroll
    const shouldScroll = useMemo(() => {
        if (!viewContainerRef.current) return false;
        const lineHeight = 20; // Approximate line height
        const maxLines = 4;
        return viewContainerRef.current.scrollHeight > lineHeight * maxLines;
    }, [value]);

    return (
        <Box
            className={`teams-style-rte ${className}`}
            sx={{
                position: 'relative',
                border: '1px solid #e0e0e0',
                borderRadius: '4px',
                backgroundColor: '#fff',
                '&:hover': {
                    borderColor: '#1976d2',
                },
                '&:focus-within': {
                    borderColor: '#1976d2',
                    boxShadow: '0 0 0 2px rgba(25, 118, 210, 0.2)',
                },
                ...style
            }}
        >
            {!isEditMode ? (
                // View Mode
                <Box
                    ref={viewContainerRef}
                    onClick={handleViewClick}
                    sx={{
                        minHeight: minHeight,
                        maxHeight: maxHeight,
                        padding: '8px 40px 8px 12px', // Extra right padding for action icon
                        cursor: disabled ? 'default' : 'text',
                        overflow: 'auto',
                        position: 'relative',
                        '& p': {
                            margin: '0 0 8px 0',
                            '&:last-child': {
                                marginBottom: 0
                            }
                        },
                        '& p:empty': {
                            display: 'none'
                        }
                    }}
                >
                    {value ? (
                        <div
                            dangerouslySetInnerHTML={{ __html: value }}
                            style={{
                                lineHeight: '1.5',
                                wordBreak: 'break-word',
                                whiteSpace: 'pre-wrap'
                            }}
                        />
                    ) : (
                        <span style={{
                            color: '#6c757d',
                            fontStyle: 'italic',
                            userSelect: 'none'
                        }}>
                            {placeholder}
                        </span>
                    )}
                </Box>
            ) : (
                // Edit Mode
                <Box
                    sx={{
                        '& .jodit-container': {
                            border: 'none !important',
                        },
                        '& .jodit-workplace': {
                            minHeight: `${minHeight} !important`,
                            maxHeight: `${maxHeight} !important`,
                            overflow: 'auto !important',
                        },
                        '& .jodit-status-bar': {
                            display: 'none !important',
                        }
                    }}
                >
                    <JoditEditor
                        ref={editorRef}
                        value={tempValue}
                        config={defaultJoditConfig}
                        onChange={handleEditorChange}
                    />
                </Box>
            )}

            {/* Floating Action Icon */}
            <Tooltip title={isEditMode ? translate("Save") : translate("Edit")}>
                <IconButton
                    onClick={handleEditClick}
                    disabled={disabled}
                    sx={{
                        position: 'absolute',
                        top: `${actionIconPosition.top}px`,
                        right: `${actionIconPosition.right}px`,
                        width: '24px',
                        height: '24px',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        border: '1px solid #e0e0e0',
                        borderRadius: '50%',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        zIndex: 10,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                            backgroundColor: '#f5f5f5',
                            borderColor: '#1976d2',
                            transform: 'scale(1.05)',
                        },
                        '& .MuiSvgIcon-root': {
                            fontSize: '16px',
                        }
                    }}
                >
                    {isEditMode ? <CheckIcon /> : <EditIcon />}
                </IconButton>
            </Tooltip>
        </Box>
    );
};

export default TeamsStyleRTE;
