import React, { useEffect, useState } from 'react';
import "./App.scss";
import GuidePopup from "./components/guideSetting/GuidePopUp";
import Drawer from "./components/drawer/Drawer";
import { AuthProvider } from "./components/auth/AuthProvider";
import { AccountProvider } from "./components/login/AccountContext";
import { SnackbarProvider } from "./components/guideSetting/guideList/SnackbarContext";
import { TranslationProvider } from "./contexts/TranslationContext";
import Rte from "./components/guideSetting/RTE";
import jwtDecode from "jwt-decode";
import useInfoStore from "./store/UserInfoStore";
import { initializeI18n } from "./multilinguial/i18n";

function App() {
	const [isI18nReady, setIsI18nReady] = useState(false);
	const accessToken = useInfoStore((state) => state.accessToken);
	const { clearAll, clearAccessToken } = useInfoStore.getState();

	// Initialize i18n once when app starts
	useEffect(() => {
		const setupI18n = async () => {
			try {
				await initializeI18n();
				console.log('✅ i18n ready for use');
				setIsI18nReady(true);
			} catch (error) {
				console.error('❌ Failed to initialize i18n:', error);
				// Set ready anyway to prevent infinite loading
				setIsI18nReady(true);
			}
		};

		setupI18n();
	}, []);

	// Check token validity
	useEffect(() => {
		if (accessToken) {
			try {
				const decodedToken: any = jwtDecode(accessToken);
				const currentTime = Math.floor(Date.now() / 1000);
				if (decodedToken.exp < currentTime) {
					console.log('🔐 Token expired, clearing session');
					clearAll();
					clearAccessToken();
				}
			} catch (error) {
				console.error('❌ Invalid token, clearing session:', error);
				clearAll();
				clearAccessToken();
			}
		}
	}, [accessToken, clearAll, clearAccessToken]);

	// Show loading until i18n is ready
	if (!isI18nReady) {
		return (
			<div className="App" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
				<div>Loading...</div>
			</div>
		);
	}

	return (
		<div className="quickadapt-extension">
			<TranslationProvider>
				<AuthProvider>
					<AccountProvider>
						<SnackbarProvider>
							<Drawer />
						</SnackbarProvider>
					</AccountProvider>
				</AuthProvider>
			</TranslationProvider>
		</div>
	);
}

export default App;
