{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useMemo } from \"react\";\nimport { Box, Tooltip, IconButton } from \"@mui/material\";\nimport EditIcon from \"@mui/icons-material/Edit\";\nimport CheckIcon from \"@mui/icons-material/Check\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, deleteicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const contentRef = useRef(\"\");\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return; // No RTE is currently being edited\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently editing RTE\n      const currentContainerRef = getContainerRef(editingRTEId);\n\n      // Check if the target is inside the currently editing RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n  const handleUpdate = (newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n  };\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n  const config = useMemo(() => ({\n    readonly: false,\n    // all options from https://xdsoft.net/jodit/docs/,\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    // Jodit uses 'direction' not just 'rtl'\n    language: 'en',\n    // Optional: change language as well\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: true,\n    cursorAfterAutofocus: 'end',\n    enter: 'br',\n    // Use <br> for line breaks instead of <p> for Teams-like behavior\n    events: {\n      onPaste: handlePaste,\n      // Attach custom onPaste handler\n      keydown: event => {\n        // Handle Enter key for Teams-style line breaks\n        if (event.key === 'Enter' && !event.shiftKey) {\n          // Allow default behavior for single line break\n          return true;\n        }\n        // Handle Escape key to exit edit mode\n        if (event.key === 'Escape') {\n          setEditingRTEId(null);\n          return false;\n        }\n      }\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [isRtlDirection]);\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const isCurrentlyEditing = editingRTEId === id;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit-workplace\": {\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : selectedTemplate === \"Announcement\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\" ? \"calc(100vh - 400px) !important\" : null,\n            overflow: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"hidden\" : \"auto !important\"\n          },\n          \".jodit-container\": {\n            minWidth: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null\n          },\n          \".jodit-toolbar__box\": {\n            display: \"flex !important\",\n            justifyContent: \"center !important\",\n            height: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null\n          }\n        },\n        className: \"qadpt-rte\",\n        children: !isCurrentlyEditing ? selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\" ? /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  height: \"24px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 49\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 49\n            }, this)]\n          }, void 0, true),\n          placement: \"top\",\n          slotProps: {\n            tooltip: {\n              sx: {\n                backgroundColor: 'white',\n                color: 'black',\n                borderRadius: '4px',\n                padding: '0px 4px',\n                border: \"1px dashed var(--primarycolor)\",\n                marginBottom: '30px !important'\n              }\n            }\n          },\n          PopperProps: {\n            modifiers: [{\n              name: 'preventOverflow',\n              options: {\n                boundary: 'viewport'\n              }\n            }, {\n              name: 'flip',\n              options: {\n                enabled: true\n              }\n            }]\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"98%\",\n              position: \"relative\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              dangerouslySetInnerHTML: {\n                __html: rteText || `<span style='color: #6c757d; font-style: italic;'>${translate(\"Start Writing ....\")}</span>`\n              },\n              onClick: () => setEditingRTEId(id),\n              sx: {\n                width: \"100%\",\n                padding: \"8px 40px 8px 12px\",\n                // Extra right padding for action icon\n                cursor: \"text\",\n                whiteSpace: \"pre-wrap\",\n                wordBreak: \"break-word\",\n                minHeight: \"40px\",\n                maxHeight: \"120px\",\n                // Approximately 4 lines\n                overflow: \"auto\",\n                lineHeight: \"1.5\",\n                border: \"1px solid #e0e0e0\",\n                borderRadius: \"4px\",\n                backgroundColor: \"#fff\",\n                position: \"relative\",\n                \"&:hover\": {\n                  borderColor: \"#1976d2\"\n                },\n                \"& p\": {\n                  margin: \"0 0 8px 0\",\n                  \"&:last-child\": {\n                    marginBottom: 0\n                  }\n                },\n                \"& p:empty\": {\n                  display: \"none\"\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 45\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: translate(\"Edit\"),\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: e => {\n                  e.stopPropagation();\n                  setEditingRTEId(id);\n                },\n                sx: {\n                  position: 'absolute',\n                  top: '8px',\n                  right: '8px',\n                  width: '24px',\n                  height: '24px',\n                  backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                  border: '1px solid #e0e0e0',\n                  borderRadius: '50%',\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                  zIndex: 10,\n                  transition: 'all 0.2s ease',\n                  '&:hover': {\n                    backgroundColor: '#f5f5f5',\n                    borderColor: '#1976d2',\n                    transform: 'scale(1.05)'\n                  },\n                  '& .MuiSvgIcon-root': {\n                    fontSize: '16px'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 53\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 37\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"98%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            dangerouslySetInnerHTML: {\n              __html: rteText || `<span style='color: #6c757d; font-style: italic;'>${translate(\"Start Writing ....\")}</span>`\n            },\n            onClick: () => setEditingRTEId(id),\n            sx: {\n              width: \"100%\",\n              padding: \"8px 40px 8px 12px\",\n              // Extra right padding for action icon\n              cursor: \"text\",\n              whiteSpace: \"pre-wrap\",\n              wordBreak: \"break-word\",\n              minHeight: \"40px\",\n              maxHeight: \"120px\",\n              // Approximately 4 lines\n              overflow: \"auto\",\n              lineHeight: \"1.5\",\n              border: \"1px solid #e0e0e0\",\n              borderRadius: \"4px\",\n              backgroundColor: \"#fff\",\n              position: \"relative\",\n              \"&:hover\": {\n                borderColor: \"#1976d2\"\n              },\n              \"& p\": {\n                margin: \"0 0 8px 0\",\n                \"&:last-child\": {\n                  marginBottom: 0\n                }\n              },\n              \"& p:empty\": {\n                display: \"none\"\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 41\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Edit\"),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: e => {\n                e.stopPropagation();\n                setEditingRTEId(id);\n              },\n              sx: {\n                position: 'absolute',\n                top: '8px',\n                right: '8px',\n                width: '24px',\n                height: '24px',\n                backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                border: '1px solid #e0e0e0',\n                borderRadius: '50%',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                zIndex: 10,\n                transition: 'all 0.2s ease',\n                '&:hover': {\n                  backgroundColor: '#f5f5f5',\n                  borderColor: '#1976d2',\n                  transform: 'scale(1.05)'\n                },\n                '& .MuiSvgIcon-root': {\n                  fontSize: '16px'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 41\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 37\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            maxWidth: \"100%\",\n            margin: \"0 auto\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: config,\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: translate(\"Save\"),\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: e => {\n                e.stopPropagation();\n                setEditingRTEId(null);\n              },\n              sx: {\n                position: 'absolute',\n                top: '8px',\n                right: '8px',\n                width: '24px',\n                height: '24px',\n                backgroundColor: 'rgba(76, 175, 80, 0.95)',\n                // Green background for save\n                border: '1px solid #4caf50',\n                borderRadius: '50%',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                zIndex: 1000,\n                // High z-index to appear above Jodit toolbar\n                transition: 'all 0.2s ease',\n                '&:hover': {\n                  backgroundColor: '#66bb6a',\n                  transform: 'scale(1.05)'\n                },\n                '& .MuiSvgIcon-root': {\n                  fontSize: '16px',\n                  color: 'white'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 33\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"1DoAZAcJTWwsxACfjPQ7z6O6k9Y=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"1DoAZAcJTWwsxACfjPQ7z6O6k9Y=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useMemo", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "EditIcon", "CheckIcon", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "contentRef", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "console", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "content", "editor", "selection", "insertHTML", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "config", "readonly", "direction", "language", "toolbarSticky", "toolbarAdaptive", "buttons", "name", "iconURL", "list", "autofocus", "cursorAfterAutofocus", "enter", "events", "onPaste", "keydown", "key", "shift<PERSON>ey", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "length", "rteContainers", "rteData", "rteBoxValue", "children", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "isCurrentlyEditing", "currentEditorRef", "sx", "display", "alignItems", "position", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "minHeight", "maxHeight", "overflow", "justifyContent", "height", "className", "title", "size", "onClick", "disabled", "backgroundColor", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "style", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placement", "slotProps", "tooltip", "color", "borderRadius", "padding", "border", "marginBottom", "PopperProps", "modifiers", "options", "boundary", "enabled", "cursor", "lineHeight", "borderColor", "margin", "e", "stopPropagation", "top", "right", "boxShadow", "zIndex", "transition", "transform", "fontSize", "max<PERSON><PERSON><PERSON>", "value", "onChange", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef,useMemo } from \"react\";\r\nimport { Box, TextField, Tooltip, IconButton } from \"@mui/material\";\r\nimport EditIcon from \"@mui/icons-material/Edit\";\r\nimport CheckIcon from \"@mui/icons-material/Check\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon } from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n        };\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n    const config = useMemo(\r\n        () => ({\r\n            readonly: false, // all options from https://xdsoft.net/jodit/docs/,\r\n            direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n            \r\n// Jodit uses 'direction' not just 'rtl'\r\n        language:  'en', // Optional: change language as well\r\n            toolbarSticky: false,\r\n            toolbarAdaptive: false,\r\n            buttons: [\r\n\r\n        'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n        'font', 'fontsize', 'link',\r\n        {\r\n            name: 'more',\r\n            iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n            list: [\r\n                        'source',\r\n                        'image', 'video', 'table',\r\n                'align', 'undo', 'redo', '|',\r\n                'hr', 'eraser', 'copyformat',\r\n                'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                'outdent', 'indent', 'paragraph',\r\n            ]\r\n        }\r\n    ],\r\n    autofocus: true,\r\n    cursorAfterAutofocus: 'end' as const,\r\n    enter: 'br' as const, // Use <br> for line breaks instead of <p> for Teams-like behavior\r\n    events: {\r\n        onPaste: handlePaste, // Attach custom onPaste handler\r\n        keydown: (event: KeyboardEvent) => {\r\n            // Handle Enter key for Teams-style line breaks\r\n            if (event.key === 'Enter' && !event.shiftKey) {\r\n                // Allow default behavior for single line break\r\n                return true;\r\n            }\r\n            // Handle Escape key to exit edit mode\r\n            if (event.key === 'Escape') {\r\n                setEditingRTEId(null);\r\n                return false;\r\n            }\r\n        }\r\n    },\r\n    controls: {\r\n        font: {\r\n            list: {\r\n                \"Poppins, sans-serif\": \"Poppins\",\r\n                \"Roboto, sans-serif\": \"Roboto\",\r\n                \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                \"Open Sans, sans-serif\": \"Open Sans\",\r\n                \"Calibri, sans-serif\": \"Calibri\",\r\n                \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n            }\r\n        }\r\n            }\r\n    }),[isRtlDirection]\r\n\r\n    );\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n                    const isCurrentlyEditing = editingRTEId === id;\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    maxHeight: (\r\n  selectedTemplate === \"Banner\" ||\r\n  (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n)\r\n  ? \"50px !important\"\r\n  : (\r\n      selectedTemplate === \"Announcement\" ||\r\n      (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\")\r\n    )\r\n    ? \"calc(100vh - 400px) !important\"\r\n    : null,\r\n                                    overflow: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ?\"hidden\" : \"auto !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minWidth:selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    minHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: \"flex !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null,\r\n                                    maxHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null\r\n                                  }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {!isCurrentlyEditing ? (\r\n\r\n                                   (selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\") || (selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") ? (\r\n                                    <Tooltip\r\n                                        title={\r\n                                            <>\r\n                                                <IconButton\r\n                                                    size=\"small\"\r\n                                                    onClick={() => handleCloneContainer(item.id)}\r\n                                                    disabled={isCloneDisabled}\r\n                                                    title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                                    sx={{\r\n                                                        \"&:hover\": {\r\n                                                            backgroundColor: \"transparent !important\",\r\n                                                        },\r\n                                                        svg: {\r\n                                                            height: \"24px\",\r\n                                                            path: {\r\n                                                                fill:\"var(--primarycolor)\"\r\n                                                            }\r\n                                                        },\r\n                                                        }}\r\n                                                    >\r\n                                                    <span\r\n                                                        dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                                        style={{\r\n                                                            opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                            height: '24px'\r\n                                                        }}\r\n                                                    />\r\n                                                </IconButton>\r\n                                                <IconButton size=\"small\" onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                                sx={{\r\n                                                    \"&:hover\": {\r\n                                                        backgroundColor: \"transparent !important\",\r\n                                                        },\r\n                                                        svg: {\r\n                                                            path: {\r\n                                                                fill:\"var(--primarycolor)\"\r\n                                                            }\r\n                                                        },\r\n                                                    }}\r\n                                                >\r\n                                                    <span dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                                        style={{\r\n                                                            height: '24px'\r\n                                                        }}\r\n                                                    />\r\n                                                </IconButton>\r\n                                            </>\r\n                                        }\r\n                                        placement=\"top\"\r\n                                        slotProps={{\r\n                                            tooltip: {\r\n                                                sx: {\r\n                                                    backgroundColor: 'white',\r\n                                                    color: 'black',\r\n                                                    borderRadius: '4px',\r\n                                                    padding: '0px 4px',\r\n                                                    border: \"1px dashed var(--primarycolor)\",\r\n                                                    marginBottom: '30px !important'\r\n                                                },\r\n                                            },\r\n                                        }}\r\n                                        PopperProps={{\r\n                                            modifiers: [\r\n                                                {\r\n                                                    name: 'preventOverflow',\r\n                                                    options: { boundary: 'viewport' },\r\n                                                },\r\n                                                {\r\n                                                    name: 'flip',\r\n                                                    options: { enabled: true },\r\n                                                },\r\n                                            ],\r\n                                        }}\r\n                                    >\r\n                                        <div style={{ width: \"98%\", position: \"relative\" }}>\r\n                                            <Box\r\n                                                dangerouslySetInnerHTML={{\r\n                                                    __html: rteText || `<span style='color: #6c757d; font-style: italic;'>${translate(\"Start Writing ....\")}</span>`\r\n                                                }}\r\n                                                onClick={() => setEditingRTEId(id)}\r\n                                                sx={{\r\n                                                    width: \"100%\",\r\n                                                    padding: \"8px 40px 8px 12px\", // Extra right padding for action icon\r\n                                                    cursor: \"text\",\r\n                                                    whiteSpace: \"pre-wrap\",\r\n                                                    wordBreak: \"break-word\",\r\n                                                    minHeight: \"40px\",\r\n                                                    maxHeight: \"120px\", // Approximately 4 lines\r\n                                                    overflow: \"auto\",\r\n                                                    lineHeight: \"1.5\",\r\n                                                    border: \"1px solid #e0e0e0\",\r\n                                                    borderRadius: \"4px\",\r\n                                                    backgroundColor: \"#fff\",\r\n                                                    position: \"relative\",\r\n                                                    \"&:hover\": {\r\n                                                        borderColor: \"#1976d2\",\r\n                                                    },\r\n                                                    \"& p\": {\r\n                                                        margin: \"0 0 8px 0\",\r\n                                                        \"&:last-child\": {\r\n                                                            marginBottom: 0\r\n                                                        }\r\n                                                    },\r\n                                                    \"& p:empty\": {\r\n                                                        display: \"none\"\r\n                                                    }\r\n                                                }}\r\n                                            />\r\n                                            {/* Floating Action Icon */}\r\n                                            <Tooltip title={translate(\"Edit\")}>\r\n                                                <IconButton\r\n                                                    onClick={(e) => {\r\n                                                        e.stopPropagation();\r\n                                                        setEditingRTEId(id);\r\n                                                    }}\r\n                                                    sx={{\r\n                                                        position: 'absolute',\r\n                                                        top: '8px',\r\n                                                        right: '8px',\r\n                                                        width: '24px',\r\n                                                        height: '24px',\r\n                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',\r\n                                                        border: '1px solid #e0e0e0',\r\n                                                        borderRadius: '50%',\r\n                                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n                                                        zIndex: 10,\r\n                                                        transition: 'all 0.2s ease',\r\n                                                        '&:hover': {\r\n                                                            backgroundColor: '#f5f5f5',\r\n                                                            borderColor: '#1976d2',\r\n                                                            transform: 'scale(1.05)',\r\n                                                        },\r\n                                                        '& .MuiSvgIcon-root': {\r\n                                                            fontSize: '16px',\r\n                                                        }\r\n                                                    }}\r\n                                                >\r\n                                                    <EditIcon />\r\n                                                </IconButton>\r\n                                            </Tooltip>\r\n                                        </div>\r\n                                    </Tooltip>\r\n                                ) : (\r\n                                    <div style={{ width: \"98%\", position: \"relative\" }}>\r\n                                        <Box\r\n                                            dangerouslySetInnerHTML={{\r\n                                                __html: rteText || `<span style='color: #6c757d; font-style: italic;'>${translate(\"Start Writing ....\")}</span>`\r\n                                            }}\r\n                                            onClick={() => setEditingRTEId(id)}\r\n                                            sx={{\r\n                                                width: \"100%\",\r\n                                                padding: \"8px 40px 8px 12px\", // Extra right padding for action icon\r\n                                                cursor: \"text\",\r\n                                                whiteSpace: \"pre-wrap\",\r\n                                                wordBreak: \"break-word\",\r\n                                                minHeight: \"40px\",\r\n                                                maxHeight: \"120px\", // Approximately 4 lines\r\n                                                overflow: \"auto\",\r\n                                                lineHeight: \"1.5\",\r\n                                                border: \"1px solid #e0e0e0\",\r\n                                                borderRadius: \"4px\",\r\n                                                backgroundColor: \"#fff\",\r\n                                                position: \"relative\",\r\n                                                \"&:hover\": {\r\n                                                    borderColor: \"#1976d2\",\r\n                                                },\r\n                                                \"& p\": {\r\n                                                    margin: \"0 0 8px 0\",\r\n                                                    \"&:last-child\": {\r\n                                                        marginBottom: 0\r\n                                                    }\r\n                                                },\r\n                                                \"& p:empty\": {\r\n                                                    display: \"none\"\r\n                                                }\r\n                                            }}\r\n                                        />\r\n                                        {/* Floating Action Icon */}\r\n                                        <Tooltip title={translate(\"Edit\")}>\r\n                                            <IconButton\r\n                                                onClick={(e) => {\r\n                                                    e.stopPropagation();\r\n                                                    setEditingRTEId(id);\r\n                                                }}\r\n                                                sx={{\r\n                                                    position: 'absolute',\r\n                                                    top: '8px',\r\n                                                    right: '8px',\r\n                                                    width: '24px',\r\n                                                    height: '24px',\r\n                                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',\r\n                                                    border: '1px solid #e0e0e0',\r\n                                                    borderRadius: '50%',\r\n                                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n                                                    zIndex: 10,\r\n                                                    transition: 'all 0.2s ease',\r\n                                                    '&:hover': {\r\n                                                        backgroundColor: '#f5f5f5',\r\n                                                        borderColor: '#1976d2',\r\n                                                        transform: 'scale(1.05)',\r\n                                                    },\r\n                                                    '& .MuiSvgIcon-root': {\r\n                                                        fontSize: '16px',\r\n                                                    }\r\n                                                }}\r\n                                            >\r\n                                                <EditIcon />\r\n                                            </IconButton>\r\n                                        </Tooltip>\r\n                                    </div>\r\n                                    )\r\n\r\n                            ) : (\r\n                                <div style={{ width: \"100%\", maxWidth: \"100%\", margin: \"0 auto\", position: \"relative\" }}>\r\n                                    <JoditEditor\r\n                                        ref={currentEditorRef}\r\n                                        value={rteText}\r\n                                        config={config}\r\n                                        onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                    />\r\n                                    {/* Floating Save Icon in Edit Mode */}\r\n                                    <Tooltip title={translate(\"Save\")}>\r\n                                        <IconButton\r\n                                            onClick={(e) => {\r\n                                                e.stopPropagation();\r\n                                                setEditingRTEId(null);\r\n                                            }}\r\n                                            sx={{\r\n                                                position: 'absolute',\r\n                                                top: '8px',\r\n                                                right: '8px',\r\n                                                width: '24px',\r\n                                                height: '24px',\r\n                                                backgroundColor: 'rgba(76, 175, 80, 0.95)', // Green background for save\r\n                                                border: '1px solid #4caf50',\r\n                                                borderRadius: '50%',\r\n                                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n                                                zIndex: 1000, // High z-index to appear above Jodit toolbar\r\n                                                transition: 'all 0.2s ease',\r\n                                                '&:hover': {\r\n                                                    backgroundColor: '#66bb6a',\r\n                                                    transform: 'scale(1.05)',\r\n                                                },\r\n                                                '& .MuiSvgIcon-root': {\r\n                                                    fontSize: '16px',\r\n                                                    color: 'white',\r\n                                                }\r\n                                            }}\r\n                                        >\r\n                                            <CheckIcon />\r\n                                        </IconButton>\r\n                                    </Tooltip>\r\n                                </div>\r\n\r\n                            )}\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAACC,OAAO,QAAQ,OAAO;AAC9E,SAASC,GAAG,EAAaC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AACnE,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,UAAU,QAAQ,6BAA6B;AAElE,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGjB,UAAU,CAAAkB,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGlC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAMgD,UAAU,GAAG9C,MAAM,CAAS,EAAE,CAAC;;EAErC;EACA,MAAM+C,UAAU,GAAG/C,MAAM,CAAoC,IAAIgD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMC,aAAa,GAAGjD,MAAM,CAA+C,IAAIgD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAME,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCJ,UAAU,CAACK,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEtD,KAAK,CAAC0D,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOR,UAAU,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEtD,KAAK,CAAC0D,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACApD,SAAS,CAAC,MAAM;IACZ,MAAM2D,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAACnB,YAAY,EAAE,OAAO,CAAC;;MAE3B,MAAMoB,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMc,mBAAmB,GAAGvB,eAAe,CAACb,YAAY,CAAC;;MAEzD;MACA,IACIoC,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE5B,OAAO,IAC5B,CAAC4B,mBAAmB,CAAC5B,OAAO,CAACmB,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACI,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACEtB,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3B;IACJ,CAAC;IAEDwB,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACd,YAAY,CAAC,CAAC;EAElB7C,SAAS,CAAC,MAAM;IACZ,IAAI6C,YAAY,EAAE;MACd,MAAMuC,SAAS,GAAGjC,YAAY,CAACN,YAAY,CAAC;MAC5C,IAAIuC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpBgC,UAAU,CAAC,MAAM;UACb;QAAA,CACH,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAACxC,YAAY,CAAC,CAAC;EAIlB,MAAMyC,YAAY,GAAGA,CAACC,UAAkB,EAAEnC,KAAa,EAAEoC,WAAmB,KAAK;IAC7EzC,UAAU,CAACM,OAAO,GAAGkC,UAAU;;IAE/B;IACA,MAAME,gBAAgB,GAAG/C,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAMqD,QAAQ,GAAGhD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAMuD,kBAAkB,GAAGD,QAAQ,IAAIrD,oBAAoB,KAAK,cAAc;IAC9E,MAAMuD,YAAY,GAAGF,QAAQ,IAAIrD,oBAAoB,KAAK,QAAQ;IAClE,MAAMwD,aAAa,GAAGH,QAAQ,KAAKrD,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5GyD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACpCrD,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpBoD,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZJ,WAAW;MACXD,UAAU,EAAEA,UAAU,CAACS,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIP,gBAAgB,EAAE;MAClB,MAAMQ,gBAAgB,GAAGtD,WAAW,GAAG,CAAC;MAExC,IAAIgD,kBAAkB,EAAE;QAAA,IAAAO,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAG3D,oBAAoB,CAAC0D,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACA3D,qBAAqB,CAAC+C,WAAW,EAAED,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAkB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAGnE,yBAAyB,CAAC2D,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACAnE,0BAA0B,CAACgD,WAAW,EAAED,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAe,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAGtD,WAAW,GAAG,CAAC;MACxC,MAAMyD,gBAAgB,IAAAQ,sBAAA,GAAGrE,oBAAoB,CAAC0D,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACA3D,qBAAqB,CAAC+C,WAAW,EAAED,UAAU,CAAC;QAC9CO,OAAO,CAACC,GAAG,CAAC,kCAAkC1D,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAAyE,sBAAA,EAAAC,sBAAA;QACHjB,OAAO,CAACkB,IAAI,CAAC,kCAAkC3E,oBAAoB,OAAO,EAAE;UACxE4D,gBAAgB;UAChBT,WAAW;UACXyB,mBAAmB,GAAAH,sBAAA,GAAEvE,oBAAoB,CAAC0D,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;YAAE4B,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACAxE,kBAAkB,CAACwD,WAAW,EAAEpC,KAAK,EAAEmC,UAAU,CAAC;MAClDO,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEA9D,mBAAmB,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD,MAAMmF,oBAAoB,GAAI5B,WAAmB,IAAK;IAClD;IACA,IAAI7D,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAACsD,WAAW,CAAC;;IAE9B;IACA,IAAI9D,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAM2F,mBAAmB,GAAGA,CAAC7B,WAAmB,EAAEpC,KAAY,KAAK;IAC/D;IACA,MAAMqC,gBAAgB,GAAG/C,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAIoD,gBAAgB,EAAE;MAClB;MACA;MACAtD,eAAe,CAACqD,WAAW,EAAEpC,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACAjB,eAAe,CAACqD,WAAW,EAAEpC,KAAK,CAAC;IACvC;;IAEA;IACA7B,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAM8F,WAAW,GAAI1D,KAA2C,IAAK;IACjEA,KAAK,CAAC2D,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG5D,KAAK,CAAC4D,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAID,YAAY,EAAE;QACdE,aAAa,CAACH,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHG,aAAa,CAACH,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHG,aAAa,CAACL,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMK,aAAa,GAAIC,OAAe,IAAK;IACvC,IAAIlF,YAAY,EAAE;MACd,MAAMuC,SAAS,GAAGjC,YAAY,CAACN,YAAY,CAAC;MAC5C,IAAIuC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpB,MAAM2E,MAAM,GAAI5C,SAAS,CAAC/B,OAAO,CAAS2E,MAAM;QAChDA,MAAM,CAACC,SAAS,CAACC,UAAU,CAACH,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EACD,MAAM,CAACI,cAAc,EAAEC,iBAAiB,CAAC,GAAGrI,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAMqI,GAAG,GAAG/D,QAAQ,CAACgE,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACF,MAAMC,MAAM,GAAGtI,OAAO,CAClB,OAAO;IACHuI,QAAQ,EAAE,KAAK;IAAE;IACjBC,SAAS,EAAER,cAAc,GAAG,KAAK,GAAY,KAAc;IAEvE;IACQS,QAAQ,EAAG,IAAI;IAAE;IACbC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAE,CAEb,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACM,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACjC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IACDC,SAAS,EAAE,IAAI;IACfC,oBAAoB,EAAE,KAAc;IACpCC,KAAK,EAAE,IAAa;IAAE;IACtBC,MAAM,EAAE;MACJC,OAAO,EAAEjC,WAAW;MAAE;MACtBkC,OAAO,EAAG5F,KAAoB,IAAK;QAC/B;QACA,IAAIA,KAAK,CAAC6F,GAAG,KAAK,OAAO,IAAI,CAAC7F,KAAK,CAAC8F,QAAQ,EAAE;UAC1C;UACA,OAAO,IAAI;QACf;QACA;QACA,IAAI9F,KAAK,CAAC6F,GAAG,KAAK,QAAQ,EAAE;UACxB3G,eAAe,CAAC,IAAI,CAAC;UACrB,OAAO,KAAK;QAChB;MACJ;IACJ,CAAC;IACD6G,QAAQ,EAAE;MACNC,IAAI,EAAE;QACFV,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACI;EACR,CAAC,CAAC,EAAC,CAACf,cAAc,CAElB,CAAC;;EAEG;EACA,MAAM1C,gBAAgB,GAAG/C,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAMqD,QAAQ,GAAGhD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAMuD,kBAAkB,GAAGD,QAAQ,IAAIrD,oBAAoB,KAAK,cAAc;EAC9E,MAAMuD,YAAY,GAAGF,QAAQ,IAAIrD,oBAAoB,KAAK,QAAQ;EAClE,MAAMwD,aAAa,GAAGH,QAAQ,KAAKrD,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAM4D,gBAAgB,GAAGtD,WAAW,GAAG,CAAC;EAExC,IAAIkH,kBAAyB,GAAG,EAAE;EAElC,IAAIpE,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACAkE,kBAAkB,GAAGjH,8BAA8B,CAACqD,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIP,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAmE,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAIvH,oBAAoB,CAAC0D,gBAAgB,CAAC,cAAA6D,sBAAA,eAAtCA,sBAAA,CAAwCzD,UAAU,EAAE;MACpDwD,kBAAkB,GAAGtH,oBAAoB,CAAC0D,gBAAgB,CAAC,CAACI,UAAU,CAAC0D,MAAM,CAAC5C,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpGV,OAAO,CAACC,GAAG,CAAC,yDAAyD1D,oBAAoB,SAAS4D,gBAAgB,GAAG,EAAE;QACnH+D,eAAe,EAAEzH,oBAAoB,CAAC0D,gBAAgB,CAAC,CAACI,UAAU,CAAC4D,MAAM;QACzEC,aAAa,EAAEL,kBAAkB,CAACI,MAAM;QACxCE,OAAO,EAAEN,kBAAkB,CAAC3C,GAAG,CAACC,CAAC,KAAK;UAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;UAAEwF,WAAW,EAAEjD,CAAC,CAACiD;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHtE,OAAO,CAACkB,IAAI,CAAC,iDAAiD3E,oBAAoB,SAAS4D,gBAAgB,EAAE,CAAC;MAC9G4D,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAG9H,aAAa;EACtC;EAEA,oBACIhB,OAAA,CAAAE,SAAA;IAAAoJ,QAAA,EACKR,kBAAkB,CAAC3C,GAAG,CAAEoD,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAInH,KAAK,GAAG,EAAE;MACd,IAAIwB,EAAE,GAAG,EAAE;MAEX,IAAKa,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACA4E,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChChH,KAAK,GAAGkH,IAAI,CAAC1F,EAAE;QACfA,EAAE,GAAG0F,IAAI,CAAC1F,EAAE;MAChB,CAAC,MAAM;QAAA,IAAA4F,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpCzH,KAAK,IAAAsH,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgB/F,EAAE;QAC1BA,EAAE,GAAG0F,IAAI,CAAC1F,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAEpB,MAAMkG,kBAAkB,GAAGjI,YAAY,KAAK+B,EAAE;MAC9C,MAAMK,mBAAmB,GAAGvB,eAAe,CAACkB,EAAE,CAAC;MAC/C,MAAMmG,gBAAgB,GAAG5H,YAAY,CAACyB,EAAE,CAAC;MAEzC,oBACI7D,OAAA,CAACX,GAAG;QAEAwB,GAAG,EAAEqD,mBAAoB;QACzB+F,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,0BAA0B,EAAE;YACxBF,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfG,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAElJ,gBAAgB,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtDmJ,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBC,SAAS,EAAErJ,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACtIqJ,SAAS,EAC3CtJ,gBAAgB,KAAK,QAAQ,IAC5BA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAEhE,iBAAiB,GAEfD,gBAAgB,KAAK,cAAc,IAClCA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAe,GAExE,gCAAgC,GAChC,IAAI;YAC0BsJ,QAAQ,EAAEvJ,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAE,QAAQ,GAAG;UAC5H,CAAC;UACD,kBAAkB,EAAE;YAChBgJ,QAAQ,EAACjJ,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACpIoJ,SAAS,EAAErJ,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD,qBAAqB,EAAE;YACnB4I,OAAO,EAAE,iBAAiB;YAC1BW,cAAc,EAAE,mBAAmB;YACnCC,MAAM,EAAEzJ,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACnIqJ,SAAS,EAAEtJ,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACnI;QACN,CAAE;QACFyJ,SAAS,EAAC,WAAW;QAAAzB,QAAA,EAEpB,CAACS,kBAAkB,GAEZ1I,gBAAgB,KAAK,cAAc,IAAIA,gBAAgB,KAAK,SAAS,IAAIA,gBAAgB,KAAK,SAAS,IAAMC,oBAAoB,KAAK,cAAc,IAAIA,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAU,gBACjOtB,OAAA,CAACV,OAAO;UACJ0L,KAAK,eACDhL,OAAA,CAAAE,SAAA;YAAAoJ,QAAA,gBACItJ,OAAA,CAACT,UAAU;cACP0L,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAM7E,oBAAoB,CAACkD,IAAI,CAAC1F,EAAE,CAAE;cAC7CsH,QAAQ,EAAEvK,eAAgB;cAC1BoK,KAAK,EAAEpK,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjHkJ,EAAE,EAAE;gBACA,SAAS,EAAE;kBACPmB,eAAe,EAAE;gBACrB,CAAC;gBACDC,GAAG,EAAE;kBACDP,MAAM,EAAE,MAAM;kBACdQ,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACA,CAAE;cAAAjC,QAAA,eAENtJ,OAAA;gBACIwL,uBAAuB,EAAE;kBAAEC,MAAM,EAAE7L;gBAAS,CAAE;gBAC9C8L,KAAK,EAAE;kBACHC,OAAO,EAAE/K,eAAe,GAAG,GAAG,GAAG,CAAC;kBAClCkK,MAAM,EAAE;gBACZ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACb/L,OAAA,CAACT,UAAU;cAAC0L,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAM5E,mBAAmB,CAACiD,IAAI,CAAC1F,EAAE,EAAExB,KAAK,CAAE;cAC5E4H,EAAE,EAAE;gBACA,SAAS,EAAE;kBACPmB,eAAe,EAAE;gBACjB,CAAC;gBACDC,GAAG,EAAE;kBACDC,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACJ,CAAE;cAAAjC,QAAA,eAEFtJ,OAAA;gBAAMwL,uBAAuB,EAAE;kBAAEC,MAAM,EAAE5L;gBAAW,CAAE;gBAClD6L,KAAK,EAAE;kBACHZ,MAAM,EAAE;gBACZ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA,eACf,CACL;UACDC,SAAS,EAAC,KAAK;UACfC,SAAS,EAAE;YACPC,OAAO,EAAE;cACLjC,EAAE,EAAE;gBACAmB,eAAe,EAAE,OAAO;gBACxBe,KAAK,EAAE,OAAO;gBACdC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,SAAS;gBAClBC,MAAM,EAAE,gCAAgC;gBACxCC,YAAY,EAAE;cAClB;YACJ;UACJ,CAAE;UACFC,WAAW,EAAE;YACTC,SAAS,EAAE,CACP;cACIxE,IAAI,EAAE,iBAAiB;cACvByE,OAAO,EAAE;gBAAEC,QAAQ,EAAE;cAAW;YACpC,CAAC,EACD;cACI1E,IAAI,EAAE,MAAM;cACZyE,OAAO,EAAE;gBAAEE,OAAO,EAAE;cAAK;YAC7B,CAAC;UAET,CAAE;UAAAtD,QAAA,eAEFtJ,OAAA;YAAK0L,KAAK,EAAE;cAAEnB,KAAK,EAAE,KAAK;cAAEH,QAAQ,EAAE;YAAW,CAAE;YAAAd,QAAA,gBAC/CtJ,OAAA,CAACX,GAAG;cACAmM,uBAAuB,EAAE;gBACrBC,MAAM,EAAEjC,OAAO,IAAI,qDAAqDzI,SAAS,CAAC,oBAAoB,CAAC;cAC3G,CAAE;cACFmK,OAAO,EAAEA,CAAA,KAAMnJ,eAAe,CAAC8B,EAAE,CAAE;cACnCoG,EAAE,EAAE;gBACAM,KAAK,EAAE,MAAM;gBACb8B,OAAO,EAAE,mBAAmB;gBAAE;gBAC9BQ,MAAM,EAAE,MAAM;gBACdrC,UAAU,EAAE,UAAU;gBACtBC,SAAS,EAAE,YAAY;gBACvBC,SAAS,EAAE,MAAM;gBACjBC,SAAS,EAAE,OAAO;gBAAE;gBACpBC,QAAQ,EAAE,MAAM;gBAChBkC,UAAU,EAAE,KAAK;gBACjBR,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBhB,eAAe,EAAE,MAAM;gBACvBhB,QAAQ,EAAE,UAAU;gBACpB,SAAS,EAAE;kBACP2C,WAAW,EAAE;gBACjB,CAAC;gBACD,KAAK,EAAE;kBACHC,MAAM,EAAE,WAAW;kBACnB,cAAc,EAAE;oBACZT,YAAY,EAAE;kBAClB;gBACJ,CAAC;gBACD,WAAW,EAAE;kBACTrC,OAAO,EAAE;gBACb;cACJ;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEF/L,OAAA,CAACV,OAAO;cAAC0L,KAAK,EAAEjK,SAAS,CAAC,MAAM,CAAE;cAAAuI,QAAA,eAC9BtJ,OAAA,CAACT,UAAU;gBACP2L,OAAO,EAAG+B,CAAC,IAAK;kBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;kBACnBnL,eAAe,CAAC8B,EAAE,CAAC;gBACvB,CAAE;gBACFoG,EAAE,EAAE;kBACAG,QAAQ,EAAE,UAAU;kBACpB+C,GAAG,EAAE,KAAK;kBACVC,KAAK,EAAE,KAAK;kBACZ7C,KAAK,EAAE,MAAM;kBACbO,MAAM,EAAE,MAAM;kBACdM,eAAe,EAAE,2BAA2B;kBAC5CkB,MAAM,EAAE,mBAAmB;kBAC3BF,YAAY,EAAE,KAAK;kBACnBiB,SAAS,EAAE,2BAA2B;kBACtCC,MAAM,EAAE,EAAE;kBACVC,UAAU,EAAE,eAAe;kBAC3B,SAAS,EAAE;oBACPnC,eAAe,EAAE,SAAS;oBAC1B2B,WAAW,EAAE,SAAS;oBACtBS,SAAS,EAAE;kBACf,CAAC;kBACD,oBAAoB,EAAE;oBAClBC,QAAQ,EAAE;kBACd;gBACJ,CAAE;gBAAAnE,QAAA,eAEFtJ,OAAA,CAACR,QAAQ;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAEV/L,OAAA;UAAK0L,KAAK,EAAE;YAAEnB,KAAK,EAAE,KAAK;YAAEH,QAAQ,EAAE;UAAW,CAAE;UAAAd,QAAA,gBAC/CtJ,OAAA,CAACX,GAAG;YACAmM,uBAAuB,EAAE;cACrBC,MAAM,EAAEjC,OAAO,IAAI,qDAAqDzI,SAAS,CAAC,oBAAoB,CAAC;YAC3G,CAAE;YACFmK,OAAO,EAAEA,CAAA,KAAMnJ,eAAe,CAAC8B,EAAE,CAAE;YACnCoG,EAAE,EAAE;cACAM,KAAK,EAAE,MAAM;cACb8B,OAAO,EAAE,mBAAmB;cAAE;cAC9BQ,MAAM,EAAE,MAAM;cACdrC,UAAU,EAAE,UAAU;cACtBC,SAAS,EAAE,YAAY;cACvBC,SAAS,EAAE,MAAM;cACjBC,SAAS,EAAE,OAAO;cAAE;cACpBC,QAAQ,EAAE,MAAM;cAChBkC,UAAU,EAAE,KAAK;cACjBR,MAAM,EAAE,mBAAmB;cAC3BF,YAAY,EAAE,KAAK;cACnBhB,eAAe,EAAE,MAAM;cACvBhB,QAAQ,EAAE,UAAU;cACpB,SAAS,EAAE;gBACP2C,WAAW,EAAE;cACjB,CAAC;cACD,KAAK,EAAE;gBACHC,MAAM,EAAE,WAAW;gBACnB,cAAc,EAAE;kBACZT,YAAY,EAAE;gBAClB;cACJ,CAAC;cACD,WAAW,EAAE;gBACTrC,OAAO,EAAE;cACb;YACJ;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEF/L,OAAA,CAACV,OAAO;YAAC0L,KAAK,EAAEjK,SAAS,CAAC,MAAM,CAAE;YAAAuI,QAAA,eAC9BtJ,OAAA,CAACT,UAAU;cACP2L,OAAO,EAAG+B,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBnL,eAAe,CAAC8B,EAAE,CAAC;cACvB,CAAE;cACFoG,EAAE,EAAE;gBACAG,QAAQ,EAAE,UAAU;gBACpB+C,GAAG,EAAE,KAAK;gBACVC,KAAK,EAAE,KAAK;gBACZ7C,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACdM,eAAe,EAAE,2BAA2B;gBAC5CkB,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBiB,SAAS,EAAE,2BAA2B;gBACtCC,MAAM,EAAE,EAAE;gBACVC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACPnC,eAAe,EAAE,SAAS;kBAC1B2B,WAAW,EAAE,SAAS;kBACtBS,SAAS,EAAE;gBACf,CAAC;gBACD,oBAAoB,EAAE;kBAClBC,QAAQ,EAAE;gBACd;cACJ,CAAE;cAAAnE,QAAA,eAEFtJ,OAAA,CAACR,QAAQ;gBAAAoM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACJ,gBAGL/L,OAAA;UAAK0L,KAAK,EAAE;YAAEnB,KAAK,EAAE,MAAM;YAAEmD,QAAQ,EAAE,MAAM;YAAEV,MAAM,EAAE,QAAQ;YAAE5C,QAAQ,EAAE;UAAW,CAAE;UAAAd,QAAA,gBACpFtJ,OAAA,CAACN,WAAW;YACRmB,GAAG,EAAEmJ,gBAAiB;YACtB2D,KAAK,EAAEnE,OAAQ;YACf9B,MAAM,EAAEA,MAAO;YACfkG,QAAQ,EAAGpJ,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEnC,KAAK,EAAEwB,EAAE;UAAE;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEF/L,OAAA,CAACV,OAAO;YAAC0L,KAAK,EAAEjK,SAAS,CAAC,MAAM,CAAE;YAAAuI,QAAA,eAC9BtJ,OAAA,CAACT,UAAU;cACP2L,OAAO,EAAG+B,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBnL,eAAe,CAAC,IAAI,CAAC;cACzB,CAAE;cACFkI,EAAE,EAAE;gBACAG,QAAQ,EAAE,UAAU;gBACpB+C,GAAG,EAAE,KAAK;gBACVC,KAAK,EAAE,KAAK;gBACZ7C,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACdM,eAAe,EAAE,yBAAyB;gBAAE;gBAC5CkB,MAAM,EAAE,mBAAmB;gBAC3BF,YAAY,EAAE,KAAK;gBACnBiB,SAAS,EAAE,2BAA2B;gBACtCC,MAAM,EAAE,IAAI;gBAAE;gBACdC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACPnC,eAAe,EAAE,SAAS;kBAC1BoC,SAAS,EAAE;gBACf,CAAC;gBACD,oBAAoB,EAAE;kBAClBC,QAAQ,EAAE,MAAM;kBAChBtB,KAAK,EAAE;gBACX;cACJ,CAAE;cAAA7C,QAAA,eAEFtJ,OAAA,CAACP,SAAS;gBAAAmM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAER,GApTIlI,EAAE;QAAA+H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqTN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QAvpB4BjM,cAAc,EAgBnCH,cAAc;AAAA,EAwoB1B,CAAC;EAAA,QAxpBgCG,cAAc,EAgBnCH,cAAc;AAAA,EAwoBzB;AAACkO,GAAA,GA1pBI1N,UAAqC;AA4pB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAwN,GAAA;AAAAC,YAAA,CAAAzN,EAAA;AAAAyN,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}